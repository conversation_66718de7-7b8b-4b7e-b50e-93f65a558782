{"name": "puppeteer-app", "private": true, "version": "0.0.0", "scripts": {"build:meta": "pnpm tsx script/build_images_meta.ts", "dev": "pnpm run build:meta && vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@types/node": "^22.15.3", "antd": "^5.24.9", "date-fns": "^4.1.0", "es-toolkit": "^1.36.0", "file-type": "^21.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.8.0", "tsx": "^4.19.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "puppeteer": "^24.7.2", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}, "packageManager": "pnpm@10.2.0"}