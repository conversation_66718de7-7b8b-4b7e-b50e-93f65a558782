import puppeteer from "puppeteer";
import fs from "node:fs";
import { writeFile } from "node:fs/promises";
import path from "node:path";

const __dirname = new URL(".", import.meta.url).pathname;

const PIXIV_ID = "133482227";

const rawCookies = JSON.parse(
  fs.readFileSync(path.join(__dirname, "cookie.json"), "utf8")
);

// Define cookie interface
interface BrowserCookie {
  name: string;
  value: string;
  domain: string;
  path?: string;
  expirationDate?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: string | null;
}

// Convert browser extension cookie format to Puppeteer format
const cookies = rawCookies
  .map((cookie: BrowserCookie) => ({
    name: cookie.name,
    value: cookie.value,
    domain: cookie.domain,
    path: cookie.path || "/",
    expires: cookie.expirationDate
      ? Math.floor(cookie.expirationDate)
      : undefined,
    httpOnly: cookie.httpOnly || false,
    secure: cookie.secure || false,
    sameSite:
      cookie.sameSite === "no_restriction"
        ? ("None" as const)
        : cookie.sameSite === "lax"
          ? ("Lax" as const)
          : cookie.sameSite === "strict"
            ? ("Strict" as const)
            : undefined,
  }))
  .filter(
    (cookie: { name: string; value: string }) => cookie.name && cookie.value
  ); // Filter out invalid cookies

async function downloadImagesWithPuppeteer(
  artworkId: string,
  outputDir = "./downloaded_images"
) {
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const browser = await puppeteer.launch({
    headless: true,
  });

  browser.setCookie(...cookies);

  try {
    const page = await browser.newPage();

    // Navigate to Pixiv first to set the domain context
    await page.goto("https://www.pixiv.net", { waitUntil: "networkidle2" });

    console.log("Navigated to Pixiv main page successfully!", page.url());

    const artworkUrl = `https://www.pixiv.net/en/artworks/${artworkId}`;

    let urls: string[] = [];

    await page.goto(artworkUrl, { waitUntil: "networkidle2" });

    console.log("Navigated to artwork page successfully!", page.url());

    const imageUrlsResponse = await page.evaluate(async (artworkId) => {
      const response = await fetch(
        `https://www.pixiv.net/ajax/illust/${artworkId}/pages?lang=en`,
        {
          credentials: "include", // Include cookies
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch image URLs: ${response.status} ${response.statusText}`
        );
      }

      return await response.json();
    }, artworkId);

    const imageUrls = imageUrlsResponse.body;

    urls = imageUrls.map(
      (i: { urls: { original: string } }) => i.urls.original
    );

    // Process each URL
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      console.log(`Processing image ${i + 1}/${urls.length}: ${url}`);

      try {
        // Navigate to the page where the image is hosted
        // This helps with cookie authentication
        const urlObj = new URL(url);
        const origin = urlObj.origin;
        await page.goto(origin, { waitUntil: "networkidle2" });

        // Get the image directly using the page context
        const imageBuffer = await page.evaluate(async (imageUrl) => {
          const response = await fetch(imageUrl, {
            credentials: "include", // Include cookies
          });

          if (!response.ok) {
            throw new Error(
              `Failed to fetch image: ${response.status} ${response.statusText}`
            );
          }

          const arrayBuffer = await response.arrayBuffer();
          return Array.from(new Uint8Array(arrayBuffer));
        }, url);

        // Convert array back to buffer
        const buffer = Buffer.from(imageBuffer);

        // Generate a filename from the URL
        const filename = path.basename(url).split("?")[0]; // Remove query parameters
        const filepath = path.join(outputDir, filename);

        // Write the image to disk
        await writeFile(filepath, buffer);

        console.log(`Downloaded: ${filepath}`);
      } catch (error) {
        console.error(`Error downloading ${url}:`, error);
      }
    }
  } finally {
    await browser.close();
  }

  console.log("Download process completed!");
}

// Example usage
async function main() {
  await downloadImagesWithPuppeteer(
    PIXIV_ID,
    `../src/assets/puppeteer_images/${PIXIV_ID}`
  );
}

main().catch(console.error);
