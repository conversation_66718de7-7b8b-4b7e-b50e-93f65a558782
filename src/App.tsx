import { useEffect, useState, useCallback } from "react";
import "./App.css";
import { Flex, Image, Select, Space, Typography, Button } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { last, orderBy } from "es-toolkit/compat";
import meta from "./assets/puppeteer_images/meta.json";
import { format as formatDate } from "date-fns";
import { useSearchParams } from "react-router";

function App() {
  const [images, setImages] = useState<string[]>([]);
  const [searchParams, setSearchParams] = useSearchParams();

  const activeFolder = searchParams.get("folder") || "";
  const currentImageIndex = Math.max(0, parseInt(searchParams.get("page") || "0", 10));

  useEffect(() => {
    if (!activeFolder) return;

    const imagePaths =
      meta
        .find((m) => m.id === activeFolder)
        ?.images.map((imagePath) => {
          const imageUrl = new URL(
            "./assets/puppeteer_images/" + imagePath.path,
            import.meta.url
          ).href;

          return imageUrl;
        }) || [];

    setImages(imagePaths);
    // Reset to first image when folder changes
    if (currentImageIndex > 0) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set("page", "0");
        return newParams;
      });
    }
  }, [activeFolder, currentImageIndex, setSearchParams]);

  useEffect(() => {
    if (!activeFolder && meta.length) {
      const lastestFolder = orderBy(meta, [(m) => m.lastModified], ["desc"])[0];

      setSearchParams({ folder: lastestFolder.id });
    }
  }, [activeFolder, setSearchParams]);

  // Ensure currentImageIndex doesn't exceed available images
  useEffect(() => {
    if (images.length > 0 && currentImageIndex >= images.length) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set("page", "0");
        return newParams;
      });
    }
  }, [images.length, currentImageIndex, setSearchParams]);

  const currentFolderData = meta.find((m) => m.id === activeFolder);

  const orderedImages = orderBy(
    images,
    [
      (imageUrl) => {
        // Extract filename from URL
        const filename = last(imageUrl.split("/")) || "";

        // Try to extract page number from different naming patterns
        // Pattern 1: filename_p00.ext, filename_p0.ext
        const pMatch = filename.match(/_p(\d+)\./);
        if (pMatch) {
          return parseInt(pMatch[1], 10);
        }

        // Pattern 2: just numbers like 01.ext, 02.ext
        const numMatch = filename.match(/^(\d+)\./);
        if (numMatch) {
          return parseInt(numMatch[1], 10);
        }

        // Fallback: use filename for sorting
        return filename;
      },
    ],
    ["asc"]
  );

  // Navigation functions
  const goToPrevious = useCallback(() => {
    const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : orderedImages.length - 1;
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", newIndex.toString());
      return newParams;
    });
  }, [currentImageIndex, orderedImages.length, setSearchParams]);

  const goToNext = useCallback(() => {
    const newIndex = currentImageIndex < orderedImages.length - 1 ? currentImageIndex + 1 : 0;
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", newIndex.toString());
      return newParams;
    });
  }, [currentImageIndex, orderedImages.length, setSearchParams]);

  // Keyboard event handler
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          event.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
        case 'ArrowDown':
          event.preventDefault();
          goToNext();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [goToPrevious, goToNext]);

  return (
    <Flex vertical gap={16}>
      <Flex vertical gap={8}>
        <Typography.Title level={2}>Puppeteer Image Gallery</Typography.Title>

        <Flex align="center" gap={16}>
          <Typography.Text strong>Select Folder:</Typography.Text>
          <Select
            value={activeFolder}
            onChange={(folder) => setSearchParams({ folder })}
            style={{ minWidth: 300 }}
            placeholder="Choose a folder"
            options={orderBy(meta, [(m) => m.lastModified], ["desc"]).map(
              (m) => ({
                label: (
                  <>
                    {m.folderName} ({m.images.length} images)&nbsp;
                    {formatDate(m.lastModified, "PPpp")}
                  </>
                ),
                value: m.id,
              })
            )}
          />
        </Flex>

        {currentFolderData && (
          <Typography.Text type="secondary">
            Showing {currentFolderData.images.length} images from "
            {currentFolderData.folderName}"
          </Typography.Text>
        )}
      </Flex>

      {orderedImages.length > 0 && (
        <Flex vertical gap={16} align="center" style={{ alignSelf: "stretch" }}>
          {/* Image counter and navigation info */}
          <Flex align="center" gap={16}>
            <Typography.Text strong>
              Page {currentImageIndex + 1} of {orderedImages.length}
            </Typography.Text>
            <Typography.Text type="secondary">
              {last(orderedImages[currentImageIndex]?.split("/")) || ""}
            </Typography.Text>
          </Flex>

          {/* Navigation buttons */}
          <Flex gap={8} align="center">
            <Button
              icon={<LeftOutlined />}
              onClick={goToPrevious}
              disabled={orderedImages.length <= 1}
              size="large"
            >
              Previous (←/↑)
            </Button>

            <Button
              icon={<RightOutlined />}
              onClick={goToNext}
              disabled={orderedImages.length <= 1}
              size="large"
            >
              Next (→/↓)
            </Button>
          </Flex>

          {/* Current image */}
          <Space>
            <Image
              style={{
                maxHeight: "80vh",
                width: "auto",
                objectFit: "contain",
              }}
              src={orderedImages[currentImageIndex]}
              alt={`Page ${currentImageIndex + 1} - ${last(orderedImages[currentImageIndex]?.split("/")) || ""}`}
            />
          </Space>

          {/* Keyboard shortcuts info */}
          <Typography.Text type="secondary" style={{ textAlign: "center" }}>
            Use arrow keys (←/→ or ↑/↓) or buttons to navigate between images
          </Typography.Text>
        </Flex>
      )}
    </Flex>
  );
}

export default App;
