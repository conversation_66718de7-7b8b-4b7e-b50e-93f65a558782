import { useEffect, useState } from "react";
import "./App.css";
import { Flex, Image, Select, Space, Typography } from "antd";
import { last, orderBy } from "es-toolkit/compat";
import meta from "./assets/puppeteer_images/meta.json";
import { format as formatDate } from "date-fns";
import { useSearchParams } from "react-router";

function App() {
  const [images, setImages] = useState<string[]>([]);
  const [searchParams, setSearchParams] = useSearchParams();

  const activeFolder = searchParams.get("folder") || "";

  useEffect(() => {
    if (!activeFolder) return;

    const imagePaths =
      meta
        .find((m) => m.id === activeFolder)
        ?.images.map((imagePath) => {
          const imageUrl = new URL(
            "./assets/puppeteer_images/" + imagePath.path,
            import.meta.url
          ).href;

          return imageUrl;
        }) || [];

    setImages(imagePaths);
  }, [activeFolder]);

  useEffect(() => {
    if (!activeFolder && meta.length) {
      const lastestFolder = orderBy(meta, [(m) => m.lastModified], ["desc"])[0];

      setSearchParams({ folder: lastestFolder.id });
    }
  }, [activeFolder, setSearchParams]);

  const currentFolderData = meta.find((m) => m.id === activeFolder);

  const orderedImages = orderBy(
    images,
    [
      (imageUrl) => {
        // Extract filename from URL
        const filename = last(imageUrl.split("/")) || "";

        // Try to extract page number from different naming patterns
        // Pattern 1: filename_p00.ext, filename_p0.ext
        const pMatch = filename.match(/_p(\d+)\./);
        if (pMatch) {
          return parseInt(pMatch[1], 10);
        }

        // Pattern 2: just numbers like 01.ext, 02.ext
        const numMatch = filename.match(/^(\d+)\./);
        if (numMatch) {
          return parseInt(numMatch[1], 10);
        }

        // Fallback: use filename for sorting
        return filename;
      },
    ],
    ["asc"]
  )

  return (
    <Flex vertical gap={16}>
      <Flex vertical gap={8}>
        <Typography.Title level={2}>Puppeteer Image Gallery</Typography.Title>

        <Flex align="center" gap={16}>
          <Typography.Text strong>Select Folder:</Typography.Text>
          <Select
            value={activeFolder}
            onChange={(folder) => setSearchParams({ folder })}
            style={{ minWidth: 300 }}
            placeholder="Choose a folder"
            options={orderBy(meta, [(m) => m.lastModified], ["desc"]).map(
              (m) => ({
                label: (
                  <>
                    {m.folderName} ({m.images.length} images)&nbsp;
                    {formatDate(m.lastModified, "PPpp")}
                  </>
                ),
                value: m.id,
              })
            )}
          />
        </Flex>

        {currentFolderData && (
          <Typography.Text type="secondary">
            Showing {currentFolderData.images.length} images from "
            {currentFolderData.folderName}"
          </Typography.Text>
        )}
      </Flex>

      {orderedImages.map((image, index) => {
        const filename = last(image.split("/")) || "";

        return (
          <Flex
            key={image}
            vertical
            gap={4}
            align="center"
            style={{ alignSelf: "stretch" }}
          >
            <Typography.Text strong>
              Page {index + 1}: {filename}
            </Typography.Text>

            <Space>
              <Image
                style={{
                  maxHeight: "90vh",
                  width: "auto",
                  objectFit: "contain",
                }}
                src={image}
                alt={`Page ${index + 1} - ${filename}`}
              />
            </Space>
          </Flex>
        );
      })}
    </Flex>
  );
}

export default App;
